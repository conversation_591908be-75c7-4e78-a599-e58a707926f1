import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { getAllScenicList, getTicketAllList } from '#/api/manageModule';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '所属景区',
      hideLabel: true,
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllScenicList,
        placeholder: '请选择所属景区',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'name',
      label: '门票名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入门票名称',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'dealGroupId',
      label: '团购ID',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入团购ID',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'saleStatus',
      label: '售卖状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择售卖状态',
        allowClear: true,
        options: [
          { label: '未开始售卖', value: 1 },
          { label: '售卖中', value: 2 },
          { label: '售卖结束', value: 3 },
        ],
      },
    },
  ];
}

export function useColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      title: '团购ID',
      field: 'dealGroupId',
      width: 120,
      align: 'center',
      fixed: 'left',
    },
    {
      title: '团购名称',
      field: 'title',
      width: 260,
      align: 'center',
    },
    {
      title: '所属景区',
      field: 'scenicInfo',
      width: 120,
      align: 'center',
      formatter: ({ row }: any) => {
        return row.scenicInfo?.scenicName;
      },
    },
    {
      title: '门票信息',
      field: 'ticketInfo',
      width: 200,
      align: 'center',
      formatter: ({ row }: any) => {
        return row.ticketInfo?.ticketName;
      },
    },
    {
      title: '团购价格',
      field: 'price',
      width: 120,
      align: 'center',
    },
    {
      title: '售卖时间',
      field: 'beginDate',
      minWidth: 250,
      align: 'center',
      formatter: ({ row }: any) => {
        return row.beginDate + ' ~ ' + row.endDate;
      },
    },
    {
      title: '服务时间',
      field: 'receiptBeginDate',
      minWidth: 250,
      align: 'center',
      formatter: ({ row }: any) => {
        return row.receiptBeginDate + ' ~ ' + row.receiptEndDate;
      },
    },
    {
      title: '售卖状态',
      field: 'saleStatus',
      width: 120,
      align: 'center',
      cellRender: {
        name: 'CellTag',
        options: [
          { value: 1, label: '未开始售卖', color: 'default' },
          { value: 2, label: '售卖中', color: 'success' },
          { value: 3, label: '售卖结束', color: 'default' },
        ],
      },
    },
    {
      title: '团购状态',
      field: 'dealGroupStatus',
      width: 120,
      align: 'center',
      formatter: ({ row }: any) => {
        return row.dealGroupStatus === 1 ? '在售团单' : '隐藏单';
      },
    },
    {
      title: '操作',
      field: 'operation',
      cellRender: {
        attrs: {
          nameField: 'title',
          nameTitle: '团购名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            text: '绑定',
            code: 'bind',
            show: (row: any) => {
              return row.ticketId == 0;
            },
          },
          {
            text: '解绑',
            code: 'unbind',
            danger: true,
            show: (row: any) => {
              return row.ticketId != 0;
            },
          },
        ],
      },
      width: 120,
      align: 'center',
      fixed: 'right',
    },
  ];
}

export function useSyncFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '同步景区',
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllScenicList,
        placeholder: '请选择需要同步的景区',
        allowClear: true,
      },
    },
  ];
}

export function useBindFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      fieldName: 'ticketId',
      label: '门票',
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.ticketName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getTicketAllList,
        placeholder: '请选择绑定的门票',
        allowClear: true,
      },
    },
  ];
}
