<script setup lang="ts">
import { Page, useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { Button } from 'ant-design-vue';
import { SyncOutlined } from '@ant-design/icons-vue';
import { useColumns, useGridFormSchema } from '../data';
import { getMeituanTuangouList } from '#/api/manageModule';
import MtSync from './mtSync.vue';
import MtBind from './mtBind.vue';
const onActionClick = ({ code, row }: { code: string; row: any }) => {
  console.log(code, row);
  if (code === 'unbind') {
  } else if (code === 'bind') {
    bindFormApi.setData(row).open();
  }
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [
      ['verificationDate', ['verificationStartDate', 'verificationEndDate']],
    ],
    schema: useGridFormSchema(),
    collapsed: true,
    submitOnChange: true,
    submitOnEnter: true,
    collapsedRows: 1,
    wrapperClass:
      'grid-cols-4 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 gap-2',
    showCollapseButton: true,
  },

  gridOptions: {
    columns: useColumns(onActionClick),
    // height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const res: any = await getMeituanTuangouList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },

    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<any>,
});

const [SyncForm, syncFormApi] = useVbenModal({
  connectedComponent: MtSync,
  destroyOnClose: true,
});

const syncTicket = () => {
  syncFormApi.setData({}).open();
};
const [BindForm, bindFormApi] = useVbenModal({
  connectedComponent: MtBind,
  destroyOnClose: true,
});
const onRefresh = async () => {
  gridApi.query();
};
</script>
<template>
  <Grid>
    <template #toolbar-actions>
      <Button type="primary" @click="syncTicket">
        <SyncOutlined />同步美团团购门票</Button
      >
    </template>
  </Grid>
  <SyncForm @success="onRefresh" />
  <BindForm @success="onRefresh" />
</template>
