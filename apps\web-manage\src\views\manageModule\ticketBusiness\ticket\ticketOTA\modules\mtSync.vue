<script setup lang="ts">
import type { Recordable } from '@vben/types';
import { Select } from 'ant-design-vue';
import { computed, ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { useSyncFormSchema } from '../data';
import { refreshMeituanTuangou } from '#/api/manageModule';
const emits = defineEmits(['success']);

const formData = ref<Recordable<any>>({});
// 表单配置
const [Form, formApi] = useVbenForm({
  schema: useSyncFormSchema(),
  showDefaultActions: false,
  // 大屏一行显示3个，中屏一行显示2个，小屏一行显示1个
  wrapperClass: 'grid-cols-1',
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
});
// 表单弹窗
const [Model, modelApi] = useVbenModal({
  confirmText: '确定同步',
  async onConfirm() {
    await handleSubmit();
  },
  onOpenChange(isOpen) {
    const data = modelApi.getData<any>();
    formApi.resetForm();
    if (isOpen) {
      if (data) {
        // 创建新对象而不是直接赋值
        const newFormData = { ...data };
        // 赋值给formData.value
        formData.value = newFormData;
        formApi.setValues(formData.value);
      }
    }
  },
});

// 处理表单数据，返回处理后的值
const processFormValues = async () => {
  const { valid } = await formApi.validate();
  if (!valid) return null;

  const values = await formApi.getValues();

  return values;
};

// 提交表单
const handleSubmit = async () => {
  const values = await processFormValues();
  if (!values) return;
  modelApi.lock();
  try {
    await refreshMeituanTuangou({ ...values });
    emits('success');
    modelApi.close();
  } catch (error) {
    modelApi.unlock();
  }
};
</script>

<template>
  <Model class="w-[500px]" title="同步美团团购门票">
    <Form> </Form>
  </Model>
</template>
